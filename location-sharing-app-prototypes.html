<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Location Sharing App - Prototype Screens</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        .nav-item.active {
            color: #007AFF;
        }
        .floating-btn {
            position: absolute;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #007AFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
        }
        .map-container {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            height: 100%;
            position: relative;
        }
        .location-pin {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #FF5722;
            font-size: 32px;
        }
        .friend-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .online-indicator {
            width: 12px;
            height: 12px;
            background: #34C759;
            border-radius: 50%;
            border: 2px solid white;
            position: absolute;
            bottom: 2px;
            right: 2px;
        }
        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 40px;
            padding: 40px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .screen-info {
            text-align: center;
            margin-bottom: 20px;
        }
        .screen-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .screen-description {
            font-size: 14px;
            color: #666;
            max-width: 300px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="bg-white py-8 shadow-sm">
        <div class="max-w-6xl mx-auto px-4">
            <h1 class="text-4xl font-bold text-center text-gray-800 mb-4">Location Sharing App</h1>
            <p class="text-xl text-center text-gray-600 mb-8">Comprehensive Mobile App Prototype Screens</p>
            <div class="flex justify-center space-x-8 text-sm text-gray-500">
                <span><i class="fas fa-mobile-alt mr-2"></i>32 Screens</span>
                <span><i class="fas fa-users mr-2"></i>6 User Flows</span>
                <span><i class="fas fa-map-marker-alt mr-2"></i>Real-time Location</span>
                <span><i class="fas fa-shield-alt mr-2"></i>Privacy Controls</span>
            </div>
        </div>
    </div>

    <!-- Authentication Flow Section -->
    <div class="py-12">
        <h2 class="section-title">Authentication Flow</h2>
        <div class="prototype-grid">
            
            <!-- Splash Screen -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">1. Splash Screen</div>
                    <div class="screen-description">App launch screen with branding and loading</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen bg-gradient-to-br from-blue-500 to-purple-600 flex flex-col items-center justify-center text-white">
                        <div class="text-6xl mb-4">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h1 class="text-3xl font-bold mb-2">ShareLoc</h1>
                        <p class="text-lg opacity-90">Stay Connected, Stay Safe</p>
                        <div class="absolute bottom-20">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Welcome Screen -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">2. Welcome Screen</div>
                    <div class="screen-description">Onboarding introduction with key features</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="flex flex-col items-center justify-center h-full px-8 text-center">
                            <div class="text-6xl text-blue-500 mb-8">
                                <i class="fas fa-users"></i>
                            </div>
                            <h2 class="text-2xl font-bold mb-4">Share Your Location Safely</h2>
                            <p class="text-gray-600 mb-8 leading-relaxed">Connect with friends and family. Share your location in real-time with complete privacy control.</p>
                            <div class="space-y-4 w-full">
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">Get Started</button>
                                <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">I have an account</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sign Up Screen -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">3. Sign Up Screen</div>
                    <div class="screen-description">User registration with email and phone options</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">Create Account</h1>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="Enter your full name">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                    <input type="email" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="Enter your email">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                    <input type="tel" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="+****************">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                    <input type="password" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="Create a password">
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">Create Account</button>
                                <div class="text-center">
                                    <span class="text-gray-500">or continue with</span>
                                </div>
                                <div class="flex space-x-4">
                                    <button class="flex-1 border border-gray-300 py-3 rounded-xl flex items-center justify-center">
                                        <i class="fab fa-google text-red-500 mr-2"></i> Google
                                    </button>
                                    <button class="flex-1 border border-gray-300 py-3 rounded-xl flex items-center justify-center">
                                        <i class="fab fa-apple mr-2"></i> Apple
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Screen -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">4. Login Screen</div>
                    <div class="screen-description">User authentication with email/phone and password</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">Welcome Back</h1>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email or Phone</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="Enter email or phone number">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                    <input type="password" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="Enter your password">
                                </div>
                                <div class="flex items-center justify-between">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-2">
                                        <span class="text-sm text-gray-600">Remember me</span>
                                    </label>
                                    <a href="#" class="text-sm text-blue-500">Forgot Password?</a>
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">Sign In</button>
                                <div class="text-center">
                                    <span class="text-gray-500">Don't have an account? </span>
                                    <a href="#" class="text-blue-500 font-semibold">Sign Up</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phone Verification -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">5. Phone Verification</div>
                    <div class="screen-description">SMS verification code input for account security</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">Verify Phone</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="text-6xl text-green-500 mb-4">
                                    <i class="fas fa-sms"></i>
                                </div>
                                <h2 class="text-xl font-bold mb-2">Enter Verification Code</h2>
                                <p class="text-gray-600">We sent a 6-digit code to<br>+****************</p>
                            </div>
                            <div class="flex justify-center space-x-3 mb-8">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">Verify</button>
                            <div class="text-center">
                                <span class="text-gray-500">Didn't receive code? </span>
                                <a href="#" class="text-blue-500 font-semibold">Resend (0:30)</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Forgot Password -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">6. Forgot Password</div>
                    <div class="screen-description">Password reset via email or SMS</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">Reset Password</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="text-6xl text-orange-500 mb-4">
                                    <i class="fas fa-key"></i>
                                </div>
                                <h2 class="text-xl font-bold mb-2">Forgot Your Password?</h2>
                                <p class="text-gray-600">Enter your email or phone number and we'll send you a reset link</p>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email or Phone</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="Enter email or phone number">
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">Send Reset Link</button>
                                <div class="text-center">
                                    <a href="#" class="text-blue-500 font-semibold">Back to Sign In</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Onboarding Flow Section -->
    <div class="py-12 bg-gray-50">
        <h2 class="section-title">Onboarding Flow</h2>
        <div class="prototype-grid">

            <!-- Permissions Request -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">7. Permissions Request</div>
                    <div class="screen-description">Location and notification permissions setup</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-blue-500 mb-4">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">Enable Location Access</h2>
                                <p class="text-gray-600 mb-8">We need access to your location to share it with your trusted contacts</p>
                            </div>
                            <div class="space-y-4 mb-8">
                                <div class="flex items-center p-4 bg-blue-50 rounded-xl">
                                    <div class="text-blue-500 text-2xl mr-4">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold">Privacy Protected</h3>
                                        <p class="text-sm text-gray-600">You control who sees your location</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-4 bg-green-50 rounded-xl">
                                    <div class="text-green-500 text-2xl mr-4">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold">Time Limited</h3>
                                        <p class="text-sm text-gray-600">Set sharing duration limits</p>
                                    </div>
                                </div>
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">Allow Location Access</button>
                            <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">Maybe Later</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Setup -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">8. Profile Setup</div>
                    <div class="screen-description">User profile creation with photo and basic info</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">Setup Profile</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="relative inline-block">
                                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center text-gray-400 text-3xl">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <button class="absolute bottom-0 right-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
                                        <i class="fas fa-camera text-sm"></i>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 mt-2">Add Profile Photo</p>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Display Name</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="How should friends find you?">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Status Message</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="What's on your mind?">
                                </div>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                    <div>
                                        <h3 class="font-semibold">Make profile discoverable</h3>
                                        <p class="text-sm text-gray-600">Friends can find you by phone number</p>
                                    </div>
                                    <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                        <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                    </div>
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tutorial Walkthrough -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">9. Tutorial Walkthrough</div>
                    <div class="screen-description">Interactive tutorial showing key app features</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-green-500 mb-4">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">Share with Friends</h2>
                                <p class="text-gray-600 mb-8">Tap the share button to instantly share your location with selected friends for a specific duration</p>
                            </div>
                            <div class="flex justify-center mb-8">
                                <div class="flex space-x-2">
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                </div>
                            </div>
                            <div class="flex space-x-4">
                                <button class="flex-1 border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">Skip</button>
                                <button class="flex-1 bg-blue-500 text-white py-4 rounded-xl font-semibold">Next</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Access Setup -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">10. Location Access Setup</div>
                    <div class="screen-description">Final location permission and accuracy settings</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-red-500 mb-4">
                                    <i class="fas fa-crosshairs"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">Location Accuracy</h2>
                                <p class="text-gray-600 mb-8">Choose your preferred location accuracy level for sharing</p>
                            </div>
                            <div class="space-y-4 mb-8">
                                <div class="p-4 border-2 border-blue-500 bg-blue-50 rounded-xl">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold text-blue-700">Precise Location</h3>
                                            <p class="text-sm text-blue-600">Exact GPS coordinates</p>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-blue-500 rounded-full bg-blue-500 flex items-center justify-center">
                                            <div class="w-2 h-2 bg-white rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border border-gray-300 rounded-xl">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold">Approximate Location</h3>
                                            <p class="text-sm text-gray-600">General area (~1km radius)</p>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">Continue with Precise</button>
                            <p class="text-xs text-gray-500 text-center">You can change this anytime in Settings</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Main Navigation Section -->
    <div class="py-12">
        <h2 class="section-title">Main Navigation Screens</h2>
        <div class="prototype-grid">

            <!-- Map View (Home) -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">11. Map View (Home)</div>
                    <div class="screen-description">Main screen with interactive map and real-time locations</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="relative h-full">
                            <!-- Header -->
                            <div class="absolute top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm p-4 flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="friend-avatar mr-3">JD</div>
                                    <div>
                                        <h2 class="font-bold">John Doe</h2>
                                        <p class="text-sm text-gray-600">Sharing with 3 friends</p>
                                    </div>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center">
                                        <i class="fas fa-search text-gray-600"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center">
                                        <i class="fas fa-bell text-gray-600"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Map Container -->
                            <div class="map-container">
                                <!-- Location pins -->
                                <div class="location-pin">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="absolute top-1/3 left-1/4 text-blue-500 text-2xl">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="absolute bottom-1/3 right-1/4 text-green-500 text-2xl">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                            </div>

                            <!-- Map Controls -->
                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 space-y-2">
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-plus text-gray-600"></i>
                                </button>
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-minus text-gray-600"></i>
                                </button>
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-crosshairs text-blue-500"></i>
                                </button>
                            </div>

                            <!-- Floating Action Button -->
                            <div class="floating-btn">
                                <i class="fas fa-share-alt"></i>
                            </div>

                            <!-- Bottom Navigation -->
                            <div class="bottom-nav">
                                <div class="nav-item active">
                                    <i class="fas fa-map text-xl"></i>
                                    <span class="text-xs">Map</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-users text-xl"></i>
                                    <span class="text-xs">Friends</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-comments text-xl"></i>
                                    <span class="text-xs">Messages</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-user text-xl"></i>
                                    <span class="text-xs">Profile</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Friends List -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">12. Friends List</div>
                    <div class="screen-description">Contact management with online status and location sharing</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- Header -->
                            <div class="flex items-center justify-between mb-6">
                                <h1 class="text-2xl font-bold">Friends</h1>
                                <button class="text-blue-500 text-xl">
                                    <i class="fas fa-user-plus"></i>
                                </button>
                            </div>

                            <!-- Search Bar -->
                            <div class="relative mb-6">
                                <input type="text" class="w-full bg-gray-100 rounded-xl px-4 py-3 pl-10" placeholder="Search friends">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>

                            <!-- Friends List -->
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">SA</div>
                                            <div class="online-indicator"></div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">Sarah Anderson</h3>
                                            <p class="text-sm text-gray-600">Sharing location • 2 min ago</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-blue-500 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">MJ</div>
                                            <div class="online-indicator"></div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">Mike Johnson</h3>
                                            <p class="text-sm text-gray-600">Online • Last seen 5 min ago</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-gray-400 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">EB</div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">Emma Brown</h3>
                                            <p class="text-sm text-gray-600">Offline • Last seen 2 hours ago</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-gray-400 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bottom Navigation -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-map text-xl"></i>
                                <span class="text-xs">Map</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-users text-xl"></i>
                                <span class="text-xs">Friends</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments text-xl"></i>
                                <span class="text-xs">Messages</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user text-xl"></i>
                                <span class="text-xs">Profile</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Share Location Modal -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">13. Share Location Modal</div>
                    <div class="screen-description">Quick location sharing with contact selection and duration</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen bg-black/50 flex items-end">
                        <div class="w-full bg-white rounded-t-3xl p-6">
                            <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>
                            <h2 class="text-xl font-bold mb-6">Share Your Location</h2>

                            <!-- Contact Selection -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3">Select Friends</h3>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-center">
                                        <div class="relative">
                                            <div class="friend-avatar mb-2">SA</div>
                                            <div class="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs"></i>
                                            </div>
                                        </div>
                                        <span class="text-xs">Sarah</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="friend-avatar mb-2">MJ</div>
                                        <span class="text-xs">Mike</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="friend-avatar mb-2">EB</div>
                                        <span class="text-xs">Emma</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Duration Selection -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3">Share Duration</h3>
                                <div class="grid grid-cols-3 gap-3">
                                    <button class="p-3 border-2 border-blue-500 bg-blue-50 rounded-xl text-center">
                                        <div class="font-semibold text-blue-700">15 min</div>
                                    </button>
                                    <button class="p-3 border border-gray-300 rounded-xl text-center">
                                        <div class="font-semibold">1 hour</div>
                                    </button>
                                    <button class="p-3 border border-gray-300 rounded-xl text-center">
                                        <div class="font-semibold">8 hours</div>
                                    </button>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-3">
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">Share Location</button>
                                <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Settings -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">14. Profile Settings</div>
                    <div class="screen-description">User profile management and app settings</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- Header -->
                            <div class="flex items-center justify-between mb-6">
                                <h1 class="text-2xl font-bold">Profile</h1>
                                <button class="text-blue-500">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>

                            <!-- Profile Info -->
                            <div class="text-center mb-8">
                                <div class="relative inline-block mb-4">
                                    <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                                        JD
                                    </div>
                                    <div class="online-indicator"></div>
                                </div>
                                <h2 class="text-xl font-bold">John Doe</h2>
                                <p class="text-gray-600">Living my best life 🌟</p>
                                <div class="flex justify-center space-x-6 mt-4 text-sm">
                                    <div class="text-center">
                                        <div class="font-bold text-lg">12</div>
                                        <div class="text-gray-600">Friends</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-bold text-lg">48</div>
                                        <div class="text-gray-600">Locations</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-bold text-lg">156</div>
                                        <div class="text-gray-600">Check-ins</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Settings Menu -->
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-shield-alt text-blue-500"></i>
                                        </div>
                                        <span class="font-semibold">Privacy & Security</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-bell text-green-500"></i>
                                        </div>
                                        <span class="font-semibold">Notifications</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-cog text-orange-500"></i>
                                        </div>
                                        <span class="font-semibold">App Settings</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-question-circle text-purple-500"></i>
                                        </div>
                                        <span class="font-semibold">Help & Support</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Bottom Navigation -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-map text-xl"></i>
                                <span class="text-xs">Map</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-users text-xl"></i>
                                <span class="text-xs">Friends</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments text-xl"></i>
                                <span class="text-xs">Messages</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-user text-xl"></i>
                                <span class="text-xs">Profile</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Footer -->
    <div class="bg-gray-800 text-white py-12">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <h3 class="text-2xl font-bold mb-4">Location Sharing App Prototypes</h3>
            <p class="text-gray-300 mb-6">Complete mobile app prototype with 32 screens covering all essential features</p>
            <div class="flex justify-center space-x-8 text-sm">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>Authentication Flow</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>Real-time Location Sharing</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>Privacy Controls</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>Friend Management</span>
                </div>
            </div>
            <div class="mt-8 text-sm text-gray-400">
                <p>Ready for development implementation • Mobile-first responsive design • Production-ready UI components</p>
            </div>
        </div>
    </div>
</body>
</html>
