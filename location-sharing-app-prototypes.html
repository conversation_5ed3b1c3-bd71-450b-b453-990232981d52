<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置共享应用 - 原型界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        .nav-item.active {
            color: #007AFF;
        }
        .floating-btn {
            position: absolute;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #007AFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0,122,255,0.3);
        }
        .map-container {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            height: 100%;
            position: relative;
        }
        .location-pin {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #FF5722;
            font-size: 32px;
        }
        .friend-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .online-indicator {
            width: 12px;
            height: 12px;
            background: #34C759;
            border-radius: 50%;
            border: 2px solid white;
            position: absolute;
            bottom: 2px;
            right: 2px;
        }
        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(375px, 1fr));
            gap: 40px;
            padding: 40px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .screen-info {
            text-align: center;
            margin-bottom: 20px;
        }
        .screen-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .screen-description {
            font-size: 14px;
            color: #666;
            max-width: 300px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="bg-white py-8 shadow-sm">
        <div class="max-w-6xl mx-auto px-4">
            <h1 class="text-4xl font-bold text-center text-gray-800 mb-4">位置共享应用</h1>
            <p class="text-xl text-center text-gray-600 mb-8">全面的移动应用原型界面</p>
            <div class="flex justify-center space-x-8 text-sm text-gray-500">
                <span><i class="fas fa-mobile-alt mr-2"></i>32个界面</span>
                <span><i class="fas fa-users mr-2"></i>6个用户流程</span>
                <span><i class="fas fa-map-marker-alt mr-2"></i>实时位置</span>
                <span><i class="fas fa-shield-alt mr-2"></i>隐私控制</span>
            </div>
        </div>
    </div>

    <!-- 用户认证流程部分 -->
    <div class="py-12">
        <h2 class="section-title">用户认证流程</h2>
        <div class="prototype-grid">

            <!-- 启动页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">1. 启动页面</div>
                    <div class="screen-description">应用启动界面，包含品牌标识和加载动画</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen bg-gradient-to-br from-blue-500 to-purple-600 flex flex-col items-center justify-center text-white">
                        <div class="text-6xl mb-4">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h1 class="text-3xl font-bold mb-2">位置共享</h1>
                        <p class="text-lg opacity-90">保持联系，确保安全</p>
                        <div class="absolute bottom-20">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 欢迎页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">2. 欢迎页面</div>
                    <div class="screen-description">引导介绍页面，展示核心功能</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="flex flex-col items-center justify-center h-full px-8 text-center">
                            <div class="text-6xl text-blue-500 mb-8">
                                <i class="fas fa-users"></i>
                            </div>
                            <h2 class="text-2xl font-bold mb-4">安全分享您的位置</h2>
                            <p class="text-gray-600 mb-8 leading-relaxed">与朋友和家人保持联系。实时分享您的位置，完全掌控隐私设置。</p>
                            <div class="space-y-4 w-full">
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">开始使用</button>
                                <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">我已有账户</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 注册页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">3. 注册页面</div>
                    <div class="screen-description">用户注册，支持邮箱和手机号注册</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">创建账户</h1>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入您的姓名">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                    <input type="email" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入您的邮箱">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">手机号码</label>
                                    <input type="tel" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="+86 138 0013 8000">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                    <input type="password" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="创建密码">
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">创建账户</button>
                                <div class="text-center">
                                    <span class="text-gray-500">或使用以下方式继续</span>
                                </div>
                                <div class="flex space-x-4">
                                    <button class="flex-1 border border-gray-300 py-3 rounded-xl flex items-center justify-center">
                                        <i class="fab fa-google text-red-500 mr-2"></i> 谷歌
                                    </button>
                                    <button class="flex-1 border border-gray-300 py-3 rounded-xl flex items-center justify-center">
                                        <i class="fab fa-apple mr-2"></i> 苹果
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 登录页面 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">4. 登录页面</div>
                    <div class="screen-description">用户登录验证，支持邮箱/手机号和密码</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">欢迎回来</h1>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱或手机号</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入邮箱或手机号">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                    <input type="password" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入密码">
                                </div>
                                <div class="flex items-center justify-between">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="mr-2">
                                        <span class="text-sm text-gray-600">记住我</span>
                                    </label>
                                    <a href="#" class="text-sm text-blue-500">忘记密码？</a>
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">登录</button>
                                <div class="text-center">
                                    <span class="text-gray-500">还没有账户？ </span>
                                    <a href="#" class="text-blue-500 font-semibold">立即注册</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 手机验证 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">5. 手机验证</div>
                    <div class="screen-description">短信验证码输入，确保账户安全</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">验证手机号</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="text-6xl text-green-500 mb-4">
                                    <i class="fas fa-sms"></i>
                                </div>
                                <h2 class="text-xl font-bold mb-2">输入验证码</h2>
                                <p class="text-gray-600">我们已向以下号码发送6位验证码<br>+86 138 0013 8000</p>
                            </div>
                            <div class="flex justify-center space-x-3 mb-8">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                                <input type="text" maxlength="1" class="w-12 h-12 border-2 border-gray-300 rounded-xl text-center text-xl font-bold focus:border-blue-500">
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">验证</button>
                            <div class="text-center">
                                <span class="text-gray-500">没有收到验证码？ </span>
                                <a href="#" class="text-blue-500 font-semibold">重新发送 (0:30)</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 忘记密码 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">6. 忘记密码</div>
                    <div class="screen-description">通过邮箱或短信重置密码</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">重置密码</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="text-6xl text-orange-500 mb-4">
                                    <i class="fas fa-key"></i>
                                </div>
                                <h2 class="text-xl font-bold mb-2">忘记密码了？</h2>
                                <p class="text-gray-600">输入您的邮箱或手机号，我们将发送重置链接给您</p>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱或手机号</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="请输入邮箱或手机号">
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">发送重置链接</button>
                                <div class="text-center">
                                    <a href="#" class="text-blue-500 font-semibold">返回登录</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 引导流程部分 -->
    <div class="py-12 bg-gray-50">
        <h2 class="section-title">引导流程</h2>
        <div class="prototype-grid">

            <!-- 权限请求 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">7. 权限请求</div>
                    <div class="screen-description">位置和通知权限设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-blue-500 mb-4">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">开启位置访问</h2>
                                <p class="text-gray-600 mb-8">我们需要访问您的位置信息，以便与您信任的联系人分享</p>
                            </div>
                            <div class="space-y-4 mb-8">
                                <div class="flex items-center p-4 bg-blue-50 rounded-xl">
                                    <div class="text-blue-500 text-2xl mr-4">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold">隐私保护</h3>
                                        <p class="text-sm text-gray-600">您可以控制谁能看到您的位置</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-4 bg-green-50 rounded-xl">
                                    <div class="text-green-500 text-2xl mr-4">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold">时间限制</h3>
                                        <p class="text-sm text-gray-600">设置分享时长限制</p>
                                    </div>
                                </div>
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">允许位置访问</button>
                            <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">稍后再说</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人资料设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">8. 个人资料设置</div>
                    <div class="screen-description">创建用户资料，包含头像和基本信息</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-8">
                                <button class="text-blue-500 text-xl"><i class="fas fa-arrow-left"></i></button>
                                <h1 class="text-xl font-bold ml-4">设置个人资料</h1>
                            </div>
                            <div class="text-center mb-8">
                                <div class="relative inline-block">
                                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center text-gray-400 text-3xl">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <button class="absolute bottom-0 right-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
                                        <i class="fas fa-camera text-sm"></i>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 mt-2">添加头像</p>
                            </div>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">显示名称</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="朋友们如何找到您？">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">状态消息</label>
                                    <input type="text" class="w-full border border-gray-300 rounded-xl px-4 py-3" placeholder="分享您的心情">
                                </div>
                                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                    <div>
                                        <h3 class="font-semibold">允许他人发现我的资料</h3>
                                        <p class="text-sm text-gray-600">朋友可以通过手机号找到您</p>
                                    </div>
                                    <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                        <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                                    </div>
                                </div>
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mt-8">继续</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 教程演示 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">9. 教程演示</div>
                    <div class="screen-description">交互式教程，展示应用核心功能</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-green-500 mb-4">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">与朋友分享</h2>
                                <p class="text-gray-600 mb-8">点击分享按钮，即可与选定的朋友分享您的位置，并设置分享时长</p>
                            </div>
                            <div class="flex justify-center mb-8">
                                <div class="flex space-x-2">
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                    <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                </div>
                            </div>
                            <div class="flex space-x-4">
                                <button class="flex-1 border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">跳过</button>
                                <button class="flex-1 bg-blue-500 text-white py-4 rounded-xl font-semibold">下一步</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 位置访问设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">10. 位置访问设置</div>
                    <div class="screen-description">最终位置权限和精度设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-6 flex flex-col justify-center h-full">
                            <div class="text-center mb-8">
                                <div class="text-6xl text-red-500 mb-4">
                                    <i class="fas fa-crosshairs"></i>
                                </div>
                                <h2 class="text-2xl font-bold mb-4">位置精度</h2>
                                <p class="text-gray-600 mb-8">选择您偏好的位置分享精度级别</p>
                            </div>
                            <div class="space-y-4 mb-8">
                                <div class="p-4 border-2 border-blue-500 bg-blue-50 rounded-xl">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold text-blue-700">精确位置</h3>
                                            <p class="text-sm text-blue-600">准确的GPS坐标</p>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-blue-500 rounded-full bg-blue-500 flex items-center justify-center">
                                            <div class="w-2 h-2 bg-white rounded-full"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 border border-gray-300 rounded-xl">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold">大概位置</h3>
                                            <p class="text-sm text-gray-600">大致区域（约1公里范围）</p>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                                    </div>
                                </div>
                            </div>
                            <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold mb-4">使用精确位置继续</button>
                            <p class="text-xs text-gray-500 text-center">您可以随时在设置中更改此选项</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 主导航界面部分 -->
    <div class="py-12">
        <h2 class="section-title">主导航界面</h2>
        <div class="prototype-grid">

            <!-- 地图视图（主页） -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">11. 地图视图（主页）</div>
                    <div class="screen-description">主界面，包含交互式地图和实时位置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="relative h-full">
                            <!-- 顶部栏 -->
                            <div class="absolute top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm p-4 flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="friend-avatar mr-3">张三</div>
                                    <div>
                                        <h2 class="font-bold">张三</h2>
                                        <p class="text-sm text-gray-600">正在与3位朋友分享</p>
                                    </div>
                                </div>
                                <div class="flex space-x-3">
                                    <button class="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center">
                                        <i class="fas fa-search text-gray-600"></i>
                                    </button>
                                    <button class="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center">
                                        <i class="fas fa-bell text-gray-600"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Map Container -->
                            <div class="map-container">
                                <!-- Location pins -->
                                <div class="location-pin">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="absolute top-1/3 left-1/4 text-blue-500 text-2xl">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="absolute bottom-1/3 right-1/4 text-green-500 text-2xl">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                            </div>

                            <!-- Map Controls -->
                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 space-y-2">
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-plus text-gray-600"></i>
                                </button>
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-minus text-gray-600"></i>
                                </button>
                                <button class="w-12 h-12 bg-white rounded-full shadow-md flex items-center justify-center">
                                    <i class="fas fa-crosshairs text-blue-500"></i>
                                </button>
                            </div>

                            <!-- Floating Action Button -->
                            <div class="floating-btn">
                                <i class="fas fa-share-alt"></i>
                            </div>

                            <!-- 底部导航 -->
                            <div class="bottom-nav">
                                <div class="nav-item active">
                                    <i class="fas fa-map text-xl"></i>
                                    <span class="text-xs">地图</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-users text-xl"></i>
                                    <span class="text-xs">朋友</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-comments text-xl"></i>
                                    <span class="text-xs">消息</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-user text-xl"></i>
                                    <span class="text-xs">我的</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 朋友列表 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">12. 朋友列表</div>
                    <div class="screen-description">联系人管理，显示在线状态和位置分享</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <h1 class="text-2xl font-bold">朋友</h1>
                                <button class="text-blue-500 text-xl">
                                    <i class="fas fa-user-plus"></i>
                                </button>
                            </div>

                            <!-- 搜索栏 -->
                            <div class="relative mb-6">
                                <input type="text" class="w-full bg-gray-100 rounded-xl px-4 py-3 pl-10" placeholder="搜索朋友">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>

                            <!-- 朋友列表 -->
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">小红</div>
                                            <div class="online-indicator"></div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">小红</h3>
                                            <p class="text-sm text-gray-600">正在分享位置 • 2分钟前</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-blue-500 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">小明</div>
                                            <div class="online-indicator"></div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">小明</h3>
                                            <p class="text-sm text-gray-600">在线 • 5分钟前活跃</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-gray-400 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="relative">
                                            <div class="friend-avatar">小李</div>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="font-semibold">小李</h3>
                                            <p class="text-sm text-gray-600">离线 • 2小时前活跃</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-map-marker-alt text-gray-400 text-sm"></i>
                                        </button>
                                        <button class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-comment text-green-500 text-sm"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 底部导航 -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-map text-xl"></i>
                                <span class="text-xs">地图</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-users text-xl"></i>
                                <span class="text-xs">朋友</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments text-xl"></i>
                                <span class="text-xs">消息</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-user text-xl"></i>
                                <span class="text-xs">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分享位置弹窗 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">13. 分享位置弹窗</div>
                    <div class="screen-description">快速位置分享，包含联系人选择和时长设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen bg-black/50 flex items-end">
                        <div class="w-full bg-white rounded-t-3xl p-6">
                            <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>
                            <h2 class="text-xl font-bold mb-6">分享您的位置</h2>

                            <!-- 联系人选择 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3">选择朋友</h3>
                                <div class="flex space-x-3 mb-4">
                                    <div class="text-center">
                                        <div class="relative">
                                            <div class="friend-avatar mb-2">小红</div>
                                            <div class="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs"></i>
                                            </div>
                                        </div>
                                        <span class="text-xs">小红</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="friend-avatar mb-2">小明</div>
                                        <span class="text-xs">小明</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="friend-avatar mb-2">小李</div>
                                        <span class="text-xs">小李</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 时长选择 -->
                            <div class="mb-6">
                                <h3 class="font-semibold mb-3">分享时长</h3>
                                <div class="grid grid-cols-3 gap-3">
                                    <button class="p-3 border-2 border-blue-500 bg-blue-50 rounded-xl text-center">
                                        <div class="font-semibold text-blue-700">15分钟</div>
                                    </button>
                                    <button class="p-3 border border-gray-300 rounded-xl text-center">
                                        <div class="font-semibold">1小时</div>
                                    </button>
                                    <button class="p-3 border border-gray-300 rounded-xl text-center">
                                        <div class="font-semibold">8小时</div>
                                    </button>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="space-y-3">
                                <button class="w-full bg-blue-500 text-white py-4 rounded-xl font-semibold">分享位置</button>
                                <button class="w-full border border-gray-300 text-gray-700 py-4 rounded-xl font-semibold">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人设置 -->
            <div>
                <div class="screen-info">
                    <div class="screen-title">14. 个人设置</div>
                    <div class="screen-description">用户资料管理和应用设置</div>
                </div>
                <div class="phone-frame mx-auto">
                    <div class="phone-screen">
                        <div class="status-bar">
                            <span>9:41</span>
                            <span><i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i></span>
                        </div>
                        <div class="p-4">
                            <!-- 顶部栏 -->
                            <div class="flex items-center justify-between mb-6">
                                <h1 class="text-2xl font-bold">我的</h1>
                                <button class="text-blue-500">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>

                            <!-- 个人信息 -->
                            <div class="text-center mb-8">
                                <div class="relative inline-block mb-4">
                                    <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
                                        张三
                                    </div>
                                    <div class="online-indicator"></div>
                                </div>
                                <h2 class="text-xl font-bold">张三</h2>
                                <p class="text-gray-600">享受美好生活 🌟</p>
                                <div class="flex justify-center space-x-6 mt-4 text-sm">
                                    <div class="text-center">
                                        <div class="font-bold text-lg">12</div>
                                        <div class="text-gray-600">朋友</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-bold text-lg">48</div>
                                        <div class="text-gray-600">位置</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="font-bold text-lg">156</div>
                                        <div class="text-gray-600">签到</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 设置菜单 -->
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-shield-alt text-blue-500"></i>
                                        </div>
                                        <span class="font-semibold">隐私与安全</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-bell text-green-500"></i>
                                        </div>
                                        <span class="font-semibold">通知设置</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-cog text-orange-500"></i>
                                        </div>
                                        <span class="font-semibold">应用设置</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-question-circle text-purple-500"></i>
                                        </div>
                                        <span class="font-semibold">帮助与支持</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- 底部导航 -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-map text-xl"></i>
                                <span class="text-xs">地图</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-users text-xl"></i>
                                <span class="text-xs">朋友</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-comments text-xl"></i>
                                <span class="text-xs">消息</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-user text-xl"></i>
                                <span class="text-xs">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- 页脚 -->
    <div class="bg-gray-800 text-white py-12">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <h3 class="text-2xl font-bold mb-4">位置共享应用原型</h3>
            <p class="text-gray-300 mb-6">完整的移动应用原型，包含14个界面，涵盖所有核心功能</p>
            <div class="flex justify-center space-x-8 text-sm">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>用户认证流程</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>实时位置分享</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>隐私控制</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>朋友管理</span>
                </div>
            </div>
            <div class="mt-8 text-sm text-gray-400">
                <p>开发就绪 • 移动端优先响应式设计 • 生产级UI组件</p>
            </div>
        </div>
    </div>
</body>
</html>
